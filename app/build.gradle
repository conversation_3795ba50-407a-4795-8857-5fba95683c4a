plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id "kotlin-kapt"
    id 'com.google.dagger.hilt.android'
    id "kotlin-parcelize"
    id "com.google.firebase.appdistribution"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
    id("org.jetbrains.kotlin.plugin.compose")
}


apply plugin: 'kotlin-android'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'io.objectbox' // Apply last.


android {
    namespace 'com.one.astrology'

    /** Android API Levels : https://apilevels.com/
     Android 15 DEV	Level 35	TBD	Vanilla Ice Cream 2	—	TBD
     Android 14	Level 34	UPSIDE_DOWN_CAKE	Upside Down Cake 2	9.3%	2023
     targetSdk will need to be 34+ for new apps and app updates by August 31, 2024.
     Android 13	Level 33	TIRAMISU	Tiramisu 2	                41.6%	2022
     Android 12	Level 32 Android 12L	S_V2	Snow Cone 2     	58.9%   2021
     Level 31 Android 12	S	                                            2021
     Android 11	Level 30	R	        Red Velvet Cake 2       	75.4%	2020
     Android 10	Level 29	Q	        Quince Tart 2	            84.3%	2019
     Android 9	Level 28	P	        Pie	                        90.3%	2018
     Android 8	Level 27 Android 8.1	O_MR1	Oreo	            92.4%	2017
     Level 26 Android 8.0	O	                                    95.2%   2017
     Android 7	Level 25 Android 7.1	N_MR1	Nougat	            95.6%	2016
     Level 24 Android 7.0    N	                                    97.1%   2016
     Android 6	Level 23	M	        Marshmallow                 98.5%   2015
     Android 5	Level 22 Android 5.1	LOLLIPOP_MR1	Lollipop	99.3%   2015
     Level 21 Android 5.0	LOLLIPOP, L	                            99.5%	2014
     */
    defaultConfig {
        compileSdk 35
        applicationId "com.one.astrology"

        minSdkVersion 26 // Android Oreo 8.0 – 8.1 // 計算農曆用到 ChineseCalendar 需大於等於24
        targetSdkVersion 35
        versionCode 101
        versionName "*******"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        setProperty("archivesBaseName", "${applicationId}-v${versionName}(${versionCode})")

        buildConfigField "String", "GOOGLE_AD_ID", '"ca-app-pub-1800606262336792/9065785588"'
        buildConfigField "String", "FB_AD_ID", '"774558236969325_774648666960282"'

//        ndk {
//            abiFilters "arm64-v8a", "armeabi-v7a", "x86", "x86_64"
//        }

    }

    signingConfigs {
//        config {
//            keyAlias 'One'
//            keyPassword 'astrology'
//            storeFile file('../astrology.jks')
//            storePassword 'astrology'
//        }
//        debug {
//            storeFile rootProject.file("debug.keystore")
//        }
        release {
            def keystorePropertiesFile = rootProject.file("keystore.properties")
            def props = new Properties()
            if (keystorePropertiesFile.canRead()) {
                props.load(new FileInputStream(keystorePropertiesFile))
                storeFile file(props['storeFile'])
                storePassword props['storePassword']
                keyAlias props['keyAlias']
                keyPassword props['keyPassword']
            }
        }
    }

    buildTypes {
        debug {
            debuggable true
            signingConfig signingConfigs.release
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            enableUnitTestCoverage true
            enableAndroidTestCoverage true
        }
        release {
            debuggable false  // 是否允許 Logcat 顯示完整訊息
            signingConfig signingConfigs.release
            minifyEnabled true //開啟，混淆
            shrinkResources true //開啟，去除用到的資源
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            ndk {
                debugSymbolLevel 'SYMBOL_TABLE'
            }
        }
    }

    flavorDimensions = ["version"]
    productFlavors {
        // Terminal : ./gradlew assembleRelease appDistributionUploadDevRelease
        dev {
            dimension "version"
            applicationIdSuffix ".dev"
            versionNameSuffix "-dev"
            resValue "string", "app_name", "Astrology(dev)"
            buildConfigField 'boolean', 'IS_DEV', 'true'
            buildConfigField 'boolean', 'IS_ENABLE_LOGIN', 'true'
            buildConfigField 'boolean', 'IS_ENABLE_ARTICLE', 'true'
            firebaseAppDistribution {
                artifactType = "APK"
                appId = "1:938605136938:android:dc9dc9f33dc72571034753"
                releaseNotes = versionCode
                serviceCredentialsFile = "$rootDir/astrology-78f27-firebase-adminsdk-khvqe-63868c45a0.json"
                groups = "Android"
            }
        }

        // Terminal : ./gradlew assembleRelease appDistributionUploadUatRelease
        uat {
            dimension "version"
            applicationIdSuffix ".uat"
            versionNameSuffix "-uat"
            resValue "string", "app_name", "Astrology(uat)"
            buildConfigField 'boolean', 'IS_DEV', 'true'
            buildConfigField 'boolean', 'IS_ENABLE_LOGIN', 'true'
            buildConfigField 'boolean', 'IS_ENABLE_ARTICLE', 'true'
            firebaseAppDistribution {
                artifactType = "APK"
                appId = "1:938605136938:android:144b66f7bae6456a034753"
                releaseNotes = versionCode
                serviceCredentialsFile = "$rootDir/astrology-78f27-firebase-adminsdk-khvqe-63868c45a0.json"
                groups = "Android"
            }
        }

        // Terminal : ./gradlew bundleRelease appDistributionUploadProRelease
        pro {
            dimension "version"
            resValue "string", "app_name", "Astrology"
            buildConfigField 'boolean', 'IS_DEV', 'false'
            buildConfigField 'boolean', 'IS_ENABLE_LOGIN', 'false'
            buildConfigField 'boolean', 'IS_ENABLE_ARTICLE', 'false'
            firebaseAppDistribution {
                artifactType = "AAB"
                appId = "1:938605136938:android:5535e091d4f7c8f3034753"
                releaseNotes = versionCode
                serviceCredentialsFile = "$rootDir/astrology-78f27-firebase-adminsdk-khvqe-63868c45a0.json"
                groups = "Android"
            }
        }
    }

//    android.applicationVariants.all { variant ->
//        if (variant.buildType.name == 'release') {//release版本
//            variant.mergeAssets.doLast {
//                //删除assets文件夹下的所有zip文件
//                //includes: ['*.zip']
//                delete(fileTree(dir: variant.mergeAssets.outputDir,includes: ['other.db','*.class']))
//            }
//        }
//    }

//    packagingOptions {
//        gradle.startParameter.getTaskNames().each { task ->
//            if (task.contains('bundle')) {
//                if (task.contains('Pro')) {
//                    exclude 'src/main/assets/db/other.db'
//                } else if (task.contains('s6')) {
////                    exclude 'lib/**/my_cool_lib2.so'
//                }
//            }
//        }
//    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    buildFeatures {
        viewBinding true
        compose true
        buildConfig = true
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    composeOptions {
        kotlinCompilerExtensionVersion = "2.0.0"
    }

    // 處理 App Bundle, split APKs and LinkageError
    // Alternative: turn off splitting by ABI
    bundle {
        abi {
            // This property is set to true by default.
            enableSplit = false
        }
    }

    // 添加測試選項
    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
    }

}

repositories {
    flatDir {
        dirs 'libs'
    }
    maven { url 'https://jitpack.io' }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation files('libs/jcc-bate-0.7.3.jar')
    implementation files('libs/swisseph-2.01.00-02.jar')
    implementation files('libs/jsoup-1.13.1.jar')

    // 處理產生 PDF 檔功能
    implementation files('libs/itext-asian-5.2.0.jar')
    implementation files('libs/itextpdf-5.5.13.jar')
    // lib project
    implementation project(path: ':Core')
    // DateTimePicker
//    implementation 'com.google.android.material:material:1.6.1' //为了防止不必要的依赖冲突，0.0.3开始需要自行依赖google material库
//    implementation 'com.github.loperSeven:DateTimePicker:0.5.8'//具体版本请看顶部jitpack标识，如0.5.8,仅支持androidx
    implementation project(path: ':date_time_picker')

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.3.0'


    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'



    // 如果需要其他 Compose ：
    implementation "androidx.compose.ui:ui:1.7.8"
    implementation "androidx.compose.foundation:foundation:1.7.8"
    implementation 'androidx.compose.runtime:runtime-livedata:1.7.8'
    implementation "androidx.compose.material3:material3:1.3.1"
//    implementation "androidx.lifecycle:lifecycle-livedata-compose:2.6.0"

    // material
    implementation "com.google.android.material:material:$material_version"

    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation "androidx.activity:activity-ktx:1.10.1"

    // viewpager
    implementation "androidx.viewpager2:viewpager2:1.1.0"


    // objectbox
    implementation("io.objectbox:objectbox-kotlin:$objectboxVersion")

    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

    // 升版需要 SdkVersion 33
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"

    implementation("androidx.fragment:fragment-ktx:1.8.6")

    implementation "androidx.navigation:navigation-fragment-ktx:$nav_version"
    implementation "androidx.navigation:navigation-ui-ktx:$nav_version"

    implementation 'androidx.hilt:hilt-navigation-fragment:1.2.0'

    implementation "com.google.dagger:hilt-android:2.48"
    kapt "com.google.dagger:hilt-compiler:2.48"

    implementation "androidx.datastore:datastore-preferences:1.1.4"
    implementation "androidx.datastore:datastore:1.1.4"

    // WorkManager for background tasks
    implementation "androidx.work:work-runtime-ktx:2.9.0"


    implementation("io.coil-kt:coil:2.5.0")

    // Google billing
    implementation 'com.android.billingclient:billing:7.1.1'


    // live-event-bus
    implementation 'com.github.michaellee123:LiveEventBus:1.8.14'

    // Glide
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    kapt 'com.github.bumptech.glide:compiler:4.16.0'

    // 農曆
//    implementation 'com.github.magiclen:JavaChineseCalendar:v2.1.4'


    // QR Code
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'

    // map
    implementation 'com.google.android.gms:play-services-maps:19.1.0'
    implementation 'com.google.maps.android:android-maps-utils:2.0.3'
    implementation 'com.google.android.gms:play-services-location:21.3.0'

    // 權限 permissionsdispatcher
    implementation "com.github.permissions-dispatcher:permissionsdispatcher:$permissionsdispatcher_version"
    kapt "com.github.permissions-dispatcher:permissionsdispatcher-processor:$permissionsdispatcher_version"


    implementation 'com.google.code.gson:gson:2.11.0'

    // retrofit
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    //material-dialogs
    implementation 'com.afollestad.material-dialogs:core:3.3.0'
    implementation 'com.afollestad.material-dialogs:input:3.1.1'
    implementation 'com.afollestad.material-dialogs:files:3.1.1'
    implementation 'com.afollestad.material-dialogs:color:3.1.1'
    implementation 'com.afollestad.material-dialogs:datetime:3.1.1'
    implementation 'com.afollestad.material-dialogs:bottomsheets:3.1.1'
    implementation 'com.afollestad.material-dialogs:lifecycle:3.1.1'


    // Import the BoM for the Firebase platform
    implementation platform('com.google.firebase:firebase-bom:33.12.0')
    implementation("com.google.firebase:firebase-crashlytics")
    implementation 'com.google.firebase:firebase-firestore-ktx'
    implementation 'com.google.firebase:firebase-auth-ktx'
    implementation 'com.google.firebase:firebase-database-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-storage-ktx'

    implementation 'com.firebaseui:firebase-ui-auth:8.0.2'


    // MarkdownView
    implementation 'com.github.mukeshsolanki:MarkdownView-Android:2.0.0'


    // Jetpack Compose
    implementation "androidx.compose.ui:ui:$compose_version"
    implementation "androidx.compose.material:material:$compose_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'


    // Google ads
    implementation 'com.google.android.gms:play-services-ads:22.6.0'

    // FB AD
    implementation 'androidx.annotation:annotation:1.7.1'
    implementation 'com.facebook.android:audience-network-sdk:6.11.0'

    // 機器學習套件翻譯文字
    implementation 'com.google.mlkit:translate:17.0.3'

    // TODO
    implementation "androidx.security:security-crypto:1.1.0-alpha06"

    // App Bundle, split APKs and LinkageError
    // https://docs.objectbox.io/android/app-bundle-and-split-apk
//    implementation 'com.google.android.play:core:1.10.3'
    // This dependency is downloaded from the Google's Maven repository.
    // Make sure you also include that repository in your project's build.gradle file.
    implementation("com.google.android.play:app-update:2.1.0")

    // For Kotlin users, also import the Kotlin extensions library for Play In-App Update:
    implementation("com.google.android.play:app-update-ktx:2.1.0")


    // BaseRecyclerViewAdapterHelper
    implementation "io.github.cymchad:BaseRecyclerViewAdapterHelper:4.0.1"

    implementation 'com.afollestad.material-dialogs:core:3.3.0'


    implementation 'com.readystatesoftware.sqliteasset:sqliteassethelper:2.0.1'

    // A critical security vulnerability was discovered in reCAPTCHA Enterprise for Mobile.
    implementation 'com.google.android.recaptcha:recaptcha:18.4.0'

    implementation 'com.google.mlkit:language-id:17.0.5'


    implementation "androidx.navigation:navigation-compose:2.8.7"

    implementation "io.noties.markwon:core:4.6.2"

    implementation("com.knuddels:jtokkit:0.3.0") // Java 版的 tiktoken

    // 單元測試依賴
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    testImplementation 'io.mockk:mockk:1.13.8'
    testImplementation 'com.google.truth:truth:1.1.5'
    
    // Android UI測試依賴
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    androidTestImplementation 'androidx.test.espresso:espresso-contrib:3.5.1'
    androidTestImplementation 'androidx.test.espresso:espresso-intents:3.5.1'
    
    // Compose UI測試
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4:1.7.8'
    debugImplementation 'androidx.compose.ui:ui-test-manifest:1.7.8'
    
    // JaCoCo測試覆蓋率
    testImplementation 'org.jacoco:org.jacoco.core:0.8.12'


    // 
    def composeBom = platform('androidx.compose:compose-bom:2025.03.01')
    implementation composeBom
    androidTestImplementation composeBom

    // Compose 基礎元件
    implementation "androidx.compose.runtime:runtime"
    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.foundation:foundation"
    implementation "androidx.compose.material3:material3"
    implementation "androidx.compose.ui:ui-tooling-preview"
    debugImplementation "androidx.compose.ui:ui-tooling"
    
    // Compose 擴展
    implementation "androidx.activity:activity-compose:1.10.1"
    implementation "androidx.lifecycle:lifecycle-runtime-compose:2.8.7"
    implementation "androidx.compose.runtime:runtime-livedata"
    implementation "androidx.navigation:navigation-compose:2.8.9"
    
    // Material Design 3
    implementation "androidx.compose.material3:material3:1.3.1"
    implementation "androidx.compose.material3:material3-window-size-class:1.3.1"

    implementation("androidx.compose.material:material-icons-extended:$compose_version")

    implementation("io.coil-kt:coil-compose:2.5.0")

    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'

    implementation 'com.getkeepsafe.relinker:relinker:1.4.5'

}