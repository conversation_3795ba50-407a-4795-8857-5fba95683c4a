package com.one.astrology.util

import com.one.core.util.LogUtil

/**
 * CSV 匯入修復測試
 * 測試包含逗號的姓名和空日期字串的處理
 */
object CsvImportFixTest {
    
    /**
     * 測試問題案例：包含逗號的姓名和空日期
     */
    fun testProblematicCases() {
        LogUtil.d("開始測試問題案例")
        
        // 測試案例：包含逗號的姓名和空日期
        val problematicLines = listOf(
            "\"<PERSON><PERSON>, <PERSON>",,41.51,-87.39,,-1.0,-1.0,,false",
            "\"<PERSON>, <PERSON>",1990-01-15 10:30,25.0337,121.5644,\"台北市, 信義區\",25.0478,121.5173,朋友,false",
            "\"<PERSON>, <PERSON>\",,22.6273,120.3014,高雄市,-1.0,-1.0,家人,true",
            "正常姓名,1985-06-20 14:15,24.1477,120.6736,台中市,24.1593,120.6661,同事,false"
        )
        
        problematicLines.forEachIndexed { index, line ->
            LogUtil.d("測試第 ${index + 1} 行: $line")
            
            try {
                // 解析 CSV 行
                val columns = CsvUtil.parseCsvLine(line)
                LogUtil.d("解析結果: $columns")
                
                if (CsvUtil.validateFieldCount(columns, 9)) {
                    val rawName = CsvUtil.getFieldSafely(columns, 0)
                    val birthdayString = CsvUtil.getFieldSafely(columns, 1)
                    
                    // 驗證並清理姓名
                    val name = CsvUtil.validateAndCleanName(rawName)
                    
                    // 使用安全的日期解析
                    val timestamp = CsvUtil.parseTimestampSafely(birthdayString)
                    
                    LogUtil.d("原始姓名: '$rawName'")
                    LogUtil.d("清理後姓名: '$name'")
                    LogUtil.d("原始日期: '$birthdayString'")
                    LogUtil.d("解析後時間戳: $timestamp")
                    LogUtil.d("出生地: '${CsvUtil.getFieldSafely(columns, 4)}'")
                    LogUtil.d("標籤: '${CsvUtil.getFieldSafely(columns, 7)}'")
                    LogUtil.d("是否隱藏: ${CsvUtil.parseBooleanSafely(CsvUtil.getFieldSafely(columns, 8))}")
                    LogUtil.d("✅ 成功處理")
                } else {
                    LogUtil.e("❌ 欄位數量不足: ${columns.size}")
                }
            } catch (e: Exception) {
                LogUtil.e("❌ 解析失敗: ${e.message}")
            }
            
            LogUtil.d("---")
        }
        
        LogUtil.d("問題案例測試完成")
    }
    
    /**
     * 測試日期格式解析
     */
    fun testDateFormats() {
        LogUtil.d("開始測試日期格式解析")
        
        val dateTestCases = listOf(
            "" to "空字串",
            "   " to "空白字串",
            "1990-01-15 10:30" to "標準格式1",
            "1990/01/15 10:30" to "標準格式2",
            "1990-01-15" to "僅日期1",
            "1990/01/15" to "僅日期2",
            "15/01/1990 10:30" to "歐洲格式",
            "01/15/1990 10:30" to "美國格式",
            "invalid date" to "無效日期",
            "2023-13-45 25:70" to "無效數值"
        )
        
        dateTestCases.forEach { (dateString, description) ->
            LogUtil.d("測試 $description: '$dateString'")
            val timestamp = CsvUtil.parseTimestampSafely(dateString)
            val date = java.util.Date(timestamp)
            LogUtil.d("結果時間戳: $timestamp")
            LogUtil.d("對應日期: $date")
            LogUtil.d("---")
        }
        
        LogUtil.d("日期格式解析測試完成")
    }
    
    /**
     * 測試姓名驗證和清理
     */
    fun testNameValidation() {
        LogUtil.d("開始測試姓名驗證和清理")
        
        val nameTestCases = listOf(
            "Michelsen, Neil" to "包含逗號的姓名",
            "  張三  " to "前後有空格",
            "" to "空字串",
            "   " to "僅空格",
            "O'Connor" to "包含撇號",
            "Jean-Pierre" to "包含連字符",
            "李四\"小名\"" to "包含雙引號",
            "王五\n換行" to "包含換行符"
        )
        
        nameTestCases.forEach { (name, description) ->
            LogUtil.d("測試 $description: '$name'")
            val cleanedName = CsvUtil.validateAndCleanName(name)
            LogUtil.d("清理後: '$cleanedName'")
            LogUtil.d("---")
        }
        
        LogUtil.d("姓名驗證和清理測試完成")
    }
    
    /**
     * 測試完整的 CSV 行解析
     */
    fun testFullCsvParsing() {
        LogUtil.d("開始測試完整的 CSV 行解析")
        
        // 模擬完整的 CSV 內容
        val csvContent = """
            姓名,生日,出生地緯度,出生地經度,出生地,居住地緯度,居住地經度,標籤,是否隱藏
            "Michelsen, Neil",,41.51,-87.39,,-1.0,-1.0,,false
            "Smith, John",1990-01-15 10:30,25.0337,121.5644,"台北市, 信義區",25.0478,121.5173,朋友,false
            "Johnson, Mary",,22.6273,120.3014,高雄市,-1.0,-1.0,家人,true
            正常姓名,1985-06-20 14:15,24.1477,120.6736,台中市,24.1593,120.6661,同事,false
            "O'Connor, Patrick",2000/12/25 15:45,53.3498,-6.2603,"Dublin, Ireland",53.3498,-6.2603,客戶,false
        """.trimIndent()
        
        val lines = csvContent.split("\n").filter { it.isNotBlank() }
        val header = lines[0]
        val dataLines = lines.drop(1)
        
        LogUtil.d("CSV 標題: $header")
        LogUtil.d("資料行數: ${dataLines.size}")
        
        var successCount = 0
        var errorCount = 0
        
        dataLines.forEachIndexed { index, line ->
            LogUtil.d("處理第 ${index + 1} 行: $line")
            
            try {
                if (line.trim().isEmpty()) {
                    return@forEachIndexed
                }
                
                val columns = CsvUtil.parseCsvLine(line)
                if (CsvUtil.validateFieldCount(columns, 9)) {
                    val rawName = CsvUtil.getFieldSafely(columns, 0)
                    val birthdayString = CsvUtil.getFieldSafely(columns, 1)
                    val name = CsvUtil.validateAndCleanName(rawName)
                    val timestamp = CsvUtil.parseTimestampSafely(birthdayString)
                    
                    LogUtil.d("✅ 成功解析: $name")
                    successCount++
                } else {
                    LogUtil.e("❌ 欄位數量不足: ${columns.size}")
                    errorCount++
                }
            } catch (e: Exception) {
                LogUtil.e("❌ 解析失敗: ${e.message}")
                errorCount++
            }
        }
        
        LogUtil.d("完整 CSV 解析測試完成")
        LogUtil.d("成功: $successCount 筆，失敗: $errorCount 筆")
    }
    
    /**
     * 執行所有測試
     */
    fun runAllTests() {
        LogUtil.d("=== CSV 匯入修復測試開始 ===")
        testProblematicCases()
        LogUtil.d("")
        testDateFormats()
        LogUtil.d("")
        testNameValidation()
        LogUtil.d("")
        testFullCsvParsing()
        LogUtil.d("=== CSV 匯入修復測試完成 ===")
    }
}
