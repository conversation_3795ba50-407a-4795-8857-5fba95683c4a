package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View.OnClickListener
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.entity.BirthData
import com.one.astrology.databinding.ItemBirthDataBinding
import com.one.astrology.util.LocationUtil
import com.one.core.util.FormatUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class BirthDataItemAdapter(
    val onClickListener: OnClickListener,
    private val onSelectionChangedListener: ((selectedCount: Int) -> Unit)? = null
) : BaseQuickAdapter<BirthData, BirthDataItemAdapter.BaseViewHolder>() {

    private fun setOnClickListener(viewHolder: BaseViewHolder, item: BirthData) {
        item.isChecked = viewHolder.binding.checkbox.isChecked
        // 通知選擇狀態變化
        onSelectionChangedListener?.invoke(getSelectedCount())
    }

    fun getSelected(): List<BirthData> {
        return items.filter { it.isChecked }
    }

    /**
     * 獲取選中項目數量
     */
    fun getSelectedCount(): Int {
        return items.count { it.isChecked }
    }

    /**
     * 全選所有項目
     */
    fun selectAll() {
        items.forEach { it.isChecked = true }
        notifyDataSetChanged()
        onSelectionChangedListener?.invoke(getSelectedCount())
    }

    /**
     * 取消全選
     */
    fun unselectAll() {
        items.forEach { it.isChecked = false }
        notifyDataSetChanged()
        onSelectionChangedListener?.invoke(getSelectedCount())
    }

    /**
     * 檢查是否全選
     */
    fun isAllSelected(): Boolean {
        return items.isNotEmpty() && items.all { it.isChecked }
    }

    /**
     * 檢查是否有選中項目
     */
    fun hasSelectedItems(): Boolean {
        return items.any { it.isChecked }
    }

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int, item: BirthData?) {
        if (item != null) {
            holder.binding.tvName.text = item.name

            if (item.birthplaceArea.isNullOrEmpty()) {
                GlobalScope.launch(Dispatchers.Main) {
                    item.birthplaceArea = LocationUtil.latLngToArea(LatLng(item.birthplaceLatitude, item.birthplaceLongitude),)
                }
            }
            holder.binding.tvBirthplace.text = item.birthplaceArea

            holder.binding.checkbox.isChecked = item.isChecked

            holder.binding.tvTag.text = item.tag
            holder.binding.ivMore.tag = item
            if (item.isHide) {
                holder.binding.tvTime.text = "資訊隱藏"
            } else {
                if (item.birthdayString.isNullOrEmpty()) {
                    item.birthdayString =
                        FormatUtils.longToString(item.birthday, "yyyy/MM/dd HH:mm")
                }
                holder.binding.tvTime.text = item.birthdayString
            }
            holder.binding.checkbox.setOnClickListener {
                setOnClickListener(holder, item)
                onClickListener.onClick(it)
            }
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemBirthDataBinding = ItemBirthDataBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)
}