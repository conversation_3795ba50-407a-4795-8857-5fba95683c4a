package com.one.astrology.ui.fragment.viewmodel

import android.content.Context
import android.net.Uri
import android.os.Environment
import android.widget.Toast
import androidx.core.net.toUri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.StorageReference
import com.one.astrology.data.PairAnalysisResult
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.topic.Analyze
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.CsvUtil
import com.one.astrology.util.EphemerisUtil
import com.one.core.util.LogUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.IOException
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Locale
import java.util.TimeZone


class SynastryAnalyzeViewModel : ViewModel() {
    private val _analysisResults = MutableLiveData<List<PairAnalysisResult>>()
    val analysisResults: LiveData<List<PairAnalysisResult>> = _analysisResults

//    private val _analysisResults = MutableStateFlow<List<PairAnalysisResult>>(emptyList())
//    val analysisResults: StateFlow<List<PairAnalysisResult>> = _analysisResults

    fun getAnalysisResults(
        context: Context,
        birthDataList: List<BirthData>
    ) {
        val pairAnalysisResults = mutableListOf<PairAnalysisResult>() // 儲存結果的清單

        viewModelScope.launch(Dispatchers.Default) {
            val topics = AssetsToObjectUtil.getTopicSataSynastryList(context)
            if (birthDataList.isNotEmpty() && topics.size >= 2) {
                for (i in 0 until birthDataList.size - 1) {
                    for (j in i + 1 until birthDataList.size) {
                        val birthDataA = birthDataList[i]
                        val birthDataB = birthDataList[j]
//                    if (birthDataB.name != name) {
//                        continue
//                    }
                        val horoscopeA = EphemerisUtil.calculate(
                            context,
                            Chart.Synastry,
                            birthDataA.name,
                            birthDataA.birthday,
                            LatLng(
                                birthDataA.birthplaceLatitude,
                                birthDataA.birthplaceLongitude
                            )
                        )

                        val horoscopeB = EphemerisUtil.calculate(
                            context,
                            Chart.Synastry,
                            birthDataB.name,
                            birthDataB.birthday,
                            LatLng(
                                birthDataB.birthplaceLatitude,
                                birthDataB.birthplaceLongitude
                            )
                        )

                        // 處理第一個主題
                        val arrayList1 = Analyze.topicItem(
                            context, Chart.Synastry, topics[0], horoscopeA, horoscopeB
                        )
                        var score1 = 0

                        arrayList1.forEach {
                            it.resultList?.forEach { _ ->
                                if (it.score != null && it.isChecked) {
                                    score1 += it.score
                                }
                            }
                        }

                        // 處理第二個主題
                        val arrayList2 = Analyze.topicItem(
                            context, Chart.Synastry, topics[1], horoscopeA, horoscopeB
                        )
                        var score2 = 0
                        arrayList2.forEach {
                            it.resultList?.forEach { _ ->
                                if (it.score != null && it.isChecked) {
                                    score2 += it.score
                                }
                            }
                        }

                        // 將結果加入清單
                        val result = PairAnalysisResult(
                            birthDataA = birthDataA,
                            birthDataB = birthDataB,
                            arrayList1,
                            arrayList2,
                            score1 = score1,
                            score2 = score2
                        )
                        LogUtil.d("$i $j - ${birthDataA.name} vs ${birthDataB.name} $score1 + $score2 = ${score1 + score2}")
                        // 更新清單並即時通知
                        pairAnalysisResults.add(result)
                        _analysisResults.postValue(pairAnalysisResults.toList()) // 使用 postValue 以避免頻繁切換執行緒
                    }
                }
            }
            // 分析完成後可以進一步處理 analysisResults，例如排序、篩選或儲存
            LogUtil.d("分析結果共計: ${pairAnalysisResults.size} 筆")

//        // 依總分排序：
//        val sortedResults = analysisResults.sortedByDescending { it.score1 + it.score2 }
//        // 只取分數大於某閾值的結果：
//        val filteredResults = analysisResults.filter { it.score1 + it.score2 > 100 }
        }
    }

    private fun timestampToDate(timestamp: Long, pattern: String = "yyyy-MM-dd HH:mm"): String {
        val instant = Instant.ofEpochMilli(timestamp) // 將毫秒時間戳轉換為 Instant
        val formatter = DateTimeFormatter.ofPattern(pattern) // 定義日期格式
            .withZone(ZoneId.systemDefault()) // 設定時區
        return formatter.format(instant) // 格式化日期
    }

    fun exportToCsv(context: Context, results: List<PairAnalysisResult>, fileName: String): File? {
        try {
            // 使用 CsvUtil 建立標題行
            val header = CsvUtil.createCsvHeader(
                "A用戶名稱", "B用戶名稱", "雙方適合度分數", "緣分 7 Point分數", "總分"
            )
            val csvContent = StringBuilder(header)

            // 將每筆結果加入到 CSV 格式
            results.forEach { result ->
                // 使用 CsvUtil 建立資料行，正確處理包含逗號的資料
                val row = CsvUtil.createCsvRow(
                    result.birthDataA.name,
                    result.birthDataB.name,
                    result.score1.toString(),
                    result.score2.toString(),
                    (result.score1 + result.score2).toString()
                ) + "\n"
                csvContent.append(row)
            }

            // 获取公共下载目录路径
            val downloadsDir = context.getExternalFilesDir(null)
//                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            // 建立檔案 /storage/emulated/0/Android/data/com.example.myapp/files/data.csv
            // context.getExternalFilesDir(null)
            val time = timestampToDate(System.currentTimeMillis())
            val csvFile = File(downloadsDir, "$fileName-$time.csv")
            csvFile.writeText(csvContent.toString())

            LogUtil.d("csvFile absolutePath ${csvFile.absolutePath}")
            uploadFileToFirebase(context, csvFile.toUri())
            return csvFile
        } catch (e: Exception) {
            e.message?.let { LogUtil.e(it) }
            return null
        }
    }

    private fun convertToTimestamp(dateString: String): Long {
        val format = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        format.timeZone = TimeZone.getDefault() // 使用系統時區
        val date = format.parse(dateString)
        return date?.time ?: 0L
    }

    private fun readCSV(filePath: String): List<BirthData> {
        val birthDataList = mutableListOf<BirthData>()

        try {
            val file = File(filePath)
            if (!file.exists()) {
                LogUtil.e("File not found: $filePath")
                return emptyList()
            }

            val bufferedReader = BufferedReader(FileReader(file))
            val lines = bufferedReader.readLines()

            if (lines.isNotEmpty()) {
                val headers = lines[0].split(",") // 第一行作為表頭
                for (i in 1 until lines.size) {
                    val row = lines[i].split(",")
                    val rowData = headers.zip(row).toMap() // 轉換為 Map 格式
                    val birthData = BirthData()
                    birthData.name = row[0]
                    birthData.birthday = convertToTimestamp(row[1])
                    birthData.birthplaceLatitude = row[2].toDouble()
                    birthData.birthplaceLongitude = row[3].toDouble()
                    birthData.birthplaceArea = row[4]
                    birthData.tag = row[5]
                    birthDataList.add(birthData)
                }
            }
            bufferedReader.close()
        } catch (e: Exception) {
            LogUtil.e("Error reading CSV file $e")
        }

        return birthDataList
    }

    // 從 Firebase Storage 下載文件到本地
    fun downloadLatestFileFromFirebase(context: Context) {
        // 建立 Firebase Storage 參考
        val storage = FirebaseStorage.getInstance()
        val storageRef = storage.reference
        val folderRef = storageRef.child("import/") // 指定 storage 資料夾

        // 获取公共下载目录路径
        val localDestinationPath =
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)

        // 列出資料夾下的所有檔案
        folderRef.listAll().addOnSuccessListener { listResult ->
            if (listResult.items.isEmpty()) {
                LogUtil.e("No files found in the folder.")
                return@addOnSuccessListener
            }

            // 找出最新的檔案
            var latestFile: StorageReference? = null
            var latestTime: Long = 0

            for (item in listResult.items) {
                item.metadata.addOnSuccessListener { metadata ->
                    val updatedTime = metadata.updatedTimeMillis // 使用檔案更新時間
                    if (updatedTime > latestTime) {
                        latestTime = updatedTime
                        latestFile = item
                    }

                    // 如果是最後一個檔案，執行下載
                    if (item == listResult.items.last()) {
                        latestFile?.let { fileRef ->
                            val localFile = File(localDestinationPath, fileRef.name)
                            try {
                                localFile.parentFile?.mkdirs()
                                localFile.createNewFile()
                            } catch (e: IOException) {
                                LogUtil.e("Failed to create local file: $e")
                                return@let
                            }

                            // 開始下載最新的檔案
                            fileRef.getFile(localFile).addOnSuccessListener {
                                LogUtil.d("Latest file successfully downloaded to: ${localFile.absolutePath}")
                                val birthDataList = readCSV(localFile.absolutePath)
                                getAnalysisResults(context, birthDataList)
                            }.addOnFailureListener { e ->
                                LogUtil.e("Failed to download file: $e")
                            }
                        } ?: LogUtil.e("Failed to find the latest file.")
                    }
                }.addOnFailureListener { e ->
                    LogUtil.e("Failed to get metadata: $e")
                }
            }
        }.addOnFailureListener { e ->
            LogUtil.e("Failed to list files in folder: $e")
        }
    }


    fun uploadFileToFirebase(context: Context, fileUri: Uri) {
        // 获取 FirebaseStorage 实例
        val storage = FirebaseStorage.getInstance()
        val storageRef = storage.reference
        // 在 Firebase Storage 中创建一个文件引用
        val fileRef = storageRef.child("uploads/${fileUri.lastPathSegment}")

        // 开始上传文件
        fileRef.putFile(fileUri).addOnSuccessListener {
            Toast.makeText(context, "文件上傳成功!", Toast.LENGTH_SHORT).show()
        }.addOnFailureListener { exception ->
            exception.message?.let { LogUtil.e(it) }
            Toast.makeText(context, "上傳失敗: ${exception.message}", Toast.LENGTH_SHORT).show()
        }
    }
}