package com.one.astrology.ui.fragment.navigation

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.AppBarLayout.LayoutParams.SCROLL_FLAG_ENTER_ALWAYS
import com.google.android.material.appbar.AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.SettingsPreferencesDataStore
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentBirthDataListBinding
import com.one.astrology.databinding.LayoutEmptyBinding
import com.one.astrology.event.AddSignRecordEvent
import com.one.astrology.event.EventKey
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.fragment.adapter.BirthDataItemAdapter
import com.one.astrology.ui.fragment.bottomSheet.ChartSelectFragment
import com.one.astrology.ui.fragment.bottomSheet.FilterFragment
import com.one.astrology.ui.fragment.dialog.BirthDataDialogFragment
import com.one.astrology.ui.fragment.viewmodel.FirestoreViewModel
import com.one.astrology.ui.fragment.viewmodel.SharedViewModel
import com.one.astrology.ui.fragment.viewmodel.SynastryAnalyzeViewModel
import com.one.astrology.util.CsvUtil
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import com.one.astrology.util.launchWhenStarted
import com.one.core.util.LogUtil
import com.one.core.view.LoadingDialog
import io.objectbox.Box
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


/**
 * 記錄頁
 */
class BirthDataListFragment : BaseFragment(R.layout.fragment_birth_data_list) {
    private var list: MutableList<BirthData> = ArrayList()
    private var birthDataBox: Box<BirthData>? = null
    private lateinit var birthDataItemAdapter: BirthDataItemAdapter
    private lateinit var binding: FragmentBirthDataListBinding
    private val sharedViewModel by viewModels<SharedViewModel>()
    private val fireStoreViewModel by viewModels<FirestoreViewModel>()
    private val synastryAnalyzeViewModel: SynastryAnalyzeViewModel by viewModels()
    private val settingViewModel by viewModels<SettingViewModel>()
    private lateinit var loadingDialog: LoadingDialog

    // 選擇模式相關變數
    private var isSelectionMode = false

    private val importCsvLauncher: ActivityResultLauncher<String> = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            loadingDialog.show()
            settingViewModel.importCsvFile(requireContext(), it)
        }
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("記錄頁", this.javaClass.simpleName)
        initData()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentBirthDataListBinding.inflate(inflater, container, false)
        return binding.root
    }

    private fun initMenu() {
        val menuHost: MenuHost = requireActivity()
        menuHost.addMenuProvider(object : MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                menuInflater.inflate(R.menu.menu_birth_data_list, menu)
            }

            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                return when (menuItem.itemId) {
                    R.id.action_export_local -> {
                        exportToCsv()
                        true
                    }

                    R.id.action_import_local -> {
                        importCsvLauncher.launch("*/*")
                        true
                    }

                    R.id.action_select_mode -> {
                        toggleSelectionMode()
                        true
                    }

                    else -> false
                }
            }
        }, viewLifecycleOwner, Lifecycle.State.RESUMED)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        loadingDialog = LoadingDialog(requireContext())
        initMenu()

        initView(view)
        observeViewModel()
        requireActivity().title = getString(R.string.record)
        LiveEventBus.get(EventKey.AddUserBirthData, AddSignRecordEvent::class.java)
            .observeStickyForever {
                if (it != null) {
                    initData()
                }
            }
        LiveEventBus.get(EventKey.UpdateUserBirthData, String::class.java).observeStickyForever {
            initData()
        }
        LiveEventBus.get(EventKey.FilterSort, Int::class.java).observeStickyForever {
            initFilterSort(it)
        }
        sharedViewModel.data.observe(viewLifecycleOwner) {
            initData()
        }
        binding.etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {

            }

            override fun onTextChanged(charSequence: CharSequence?, p1: Int, p2: Int, p3: Int) {
                filterList(charSequence.toString())
            }

            override fun afterTextChanged(p0: Editable?) {

            }
        })
        binding.ivFilter.setOnClickListener {
            showFilter()
        }

        // 初始化選擇模式 UI
        initSelectionModeUI()

        // 處理返回鍵
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (isSelectionMode) {
                    exitSelectionMode()
                } else {
                    isEnabled = false
                    requireActivity().onBackPressed()
                }
            }
        })
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initFilterSort(it: Int) {
        birthDataItemAdapter.items = ArrayList()
        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
        val birthDataList: MutableList<BirthData>
        when (it) {
            R.id.rbTimeAsc -> {
                birthDataList = birthDataBox.query().order(BirthData_.id).build().find()
            }

            R.id.rbTimeDesc -> {
                birthDataList = birthDataBox.query().orderDesc(BirthData_.id).build().find()
            }

            R.id.rbBirthdayAsc -> {
                birthDataList = birthDataBox.query().order(BirthData_.birthday).build().find()
            }

            R.id.rbBirthdayDesc -> {
                birthDataList =
                    birthDataBox.query().orderDesc(BirthData_.birthday).build().find()
            }

            R.id.rbNameAsc -> {
                birthDataList = birthDataBox.query().order(BirthData_.name).build().find()
            }

            R.id.rbNameDesc -> {
                birthDataList = birthDataBox.query().orderDesc(BirthData_.name).build().find()
            }

            else -> {
                birthDataList = birthDataBox.query().order(BirthData_.id).build().find()
            }
        }
        birthDataItemAdapter.items = birthDataList
        birthDataItemAdapter.notifyDataSetChanged()
        birthDataItemAdapter.isEmptyViewEnable = true
    }

    private fun showFilter() {
        FilterFragment().show(requireActivity().supportFragmentManager, "")
    }

    fun filterList(text: String) {
        val arrayList = list.filter {
            it.name.lowercase().contains(text.lowercase()) ||
                    it.tag.lowercase().contains(text.lowercase()) ||
                    it.birthdayString?.lowercase()?.contains(text.lowercase()) ?: false ||
                    it.birthplaceArea?.lowercase()?.contains(text.lowercase()) ?: false
        }
        birthDataItemAdapter.submitList(arrayList.toMutableList())
    }

    override fun initView(view: View) {
        birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
        initRecycleView()

        binding.fabAdd.setOnClickListener {
            showBirthDataFragment()
        }
    }

    private fun showBirthDataFragment() {
        val dialog = BirthDataDialogFragment()
        dialog.show(requireActivity().supportFragmentManager, "")
    }

    private fun initData() {
        launchWhenStarted {
            val loadingDialog = LoadingDialog(requireActivity())
            loadingDialog.show()
            val it = SettingsPreferencesDataStore.getFilterSort(requireContext())
            initFilterSort(it)
            loadingDialog.dismiss()
        }
    }

    private fun initRecycleView() {
        list = birthDataBox!!.query().orderDesc(BirthData_.id).build().find()
        val layoutParams = binding.toolbar.layoutParams as AppBarLayout.LayoutParams
        if (list.isEmpty()) {
            layoutParams.scrollFlags = 0
        } else {
            layoutParams.scrollFlags = SCROLL_FLAG_SCROLL or SCROLL_FLAG_ENTER_ALWAYS
        }
        birthDataItemAdapter = BirthDataItemAdapter(
            onClickListener = {
                if (!isSelectionMode && birthDataItemAdapter.getSelected().size == 2) {
                    val userBirthDataA = birthDataItemAdapter.getSelected()[0]
                    val userBirthDataB = birthDataItemAdapter.getSelected()[1]
                    ChartSelectFragment.newInstance {
                        val chart = it.tag as Chart
                        val bundle = Bundle()
                        bundle.putParcelable(KeyDefine.UserBirthDataA, userBirthDataA)
                        bundle.putParcelable(KeyDefine.UserBirthDataB, userBirthDataB)
                        bundle.putString(KeyDefine.Chart, chart.toStorageValue())
                        startActivity(SignDetailActivity::class.java, bundle)
                    }.show(
                        requireActivity().supportFragmentManager,
                        "ChartSelectFragment"
                    )
                }
            },
            onSelectionChangedListener = { selectedCount ->
                updateSelectionUI(selectedCount)
            }
        )

        val layoutManager = LinearLayoutManager(activity)
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerView.layoutManager = layoutManager

        birthDataItemAdapter.isEmptyViewEnable = false
        val layoutEmpty = LayoutEmptyBinding.inflate(requireActivity().layoutInflater)
        layoutEmpty.tvEmptyDesc.text = getString(R.string.there_are_no_records_yet)
        birthDataItemAdapter.emptyView = layoutEmpty.root

        birthDataItemAdapter.addOnItemChildClickListener(R.id.ivMore) { adapter, view, position ->
            if (view.id == R.id.ivMore) {
                showMenu(view)
            }
        }

        birthDataItemAdapter.setOnItemClickListener { _: BaseQuickAdapter<*, *>?, _: View?, position: Int ->
            val birthData = birthDataItemAdapter.items[position]
            val bundle = Bundle()
            bundle.putParcelable(KeyDefine.UserBirthDataA, birthData)
            bundle.putString(KeyDefine.Chart, Chart.Natal.toStorageValue())
            startActivity(SignDetailActivity::class.java, bundle)
        }
        binding.recyclerView.adapter = birthDataItemAdapter
        binding.recyclerView.setOnScrollChangeListener { _, _, _, _, oldScrollY ->
            if (oldScrollY < 0) {
                binding.fabAdd.hide()
            } else {
                binding.fabAdd.show()
            }
        }
    }

    private fun showMenu(view: View) {
        val popup = PopupMenu(requireContext(), view)
        popup.menuInflater.inflate(R.menu.popup_menu, popup.menu)
        val birthData = view.tag as BirthData
        popup.setOnMenuItemClickListener {
            when (it.itemId) {
                R.id.option_share -> {
                    shareData(birthData)
                }

                R.id.option_edit -> {
                    editData(birthData)
                }

                R.id.option_remove -> {
                    removeData(birthData)
                }
            }
            true
        }
        popup.setOnDismissListener {
            // Respond to popup being dismissed.
        }
        popup.show()
    }

    private fun shareData(birthData: BirthData) {
        fireStoreViewModel.uploadData(birthData)
    }

    private fun editData(birthData: BirthData) {
        if (!birthData.isHide) {
            val dialog = BirthDataDialogFragment(birthData)
            dialog.show(requireActivity().supportFragmentManager, "")
        } else {
            Toast.makeText(
                requireActivity(),
                getString(R.string.information_is_hidden_and_cannot_be_edited), Toast.LENGTH_LONG
            ).show()
        }
    }

    private fun removeData(birthData: BirthData) {
        val dialog = MaterialDialog(requireContext(), MaterialDialog.DEFAULT_BEHAVIOR)
        dialog.message(null, getString(R.string.whether_you_want_to_delete_this_record), null)
        dialog.positiveButton(null, getString(R.string.yes)) {
            birthDataBox!!.remove(birthData)
            initData()
        }
        dialog.negativeButton(null, getString(R.string.no)) {
            LogUtil.d()
            dialog.dismiss()
        }
        dialog.show()
    }

    /**
     * 匯出星盤資料為 CSV 檔案
     */
    private fun exportToCsv() {
        try {
            val list = birthDataBox!!.query().orderDesc(BirthData_.id).build().find()
            if (list.isEmpty()) {
                Toast.makeText(requireContext(), "沒有可匯出的資料", Toast.LENGTH_SHORT).show()
                return
            }

            // 使用 CsvUtil 建立標題行
            val csvHeader = CsvUtil.createCsvHeader(
                "姓名", "生日", "出生地緯度", "出生地經度", "出生地",
                "居住地緯度", "居住地經度", "標籤", "是否隱藏"
            )
            val csvData = StringBuilder(csvHeader)

            list.forEach { birthData ->
                // 使用 CsvUtil 建立資料行，正確處理包含逗號的資料
                val row = CsvUtil.createCsvRow(
                    birthData.name,
                    birthData.birthdayString ?: "",
                    birthData.birthplaceLatitude.toString(),
                    birthData.birthplaceLongitude.toString(),
                    birthData.birthplaceArea ?: "",
                    birthData.residenceLatitude?.toString() ?: "",
                    birthData.residenceLongitude?.toString() ?: "",
                    birthData.tag,
                    birthData.isHide.toString()
                ) + "\n"
                csvData.append(row)
            }

            val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
            val timestamp = dateFormat.format(Date())
            val fileName = "Astrology_birth_data_${timestamp}.csv"
            val file = File(requireContext().getExternalFilesDir(null), fileName)
            file.writeText(csvData.toString())

            val fileUri = androidx.core.content.FileProvider.getUriForFile(
                requireContext(),
                "${requireContext().packageName}.fileprovider",
                file
            )

            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "text/csv"
                putExtra(Intent.EXTRA_STREAM, fileUri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                putExtra(Intent.EXTRA_SUBJECT, fileName)
            }
            startActivity(Intent.createChooser(intent, "匯出 CSV 檔案"))

            Toast.makeText(requireContext(), "匯出成功", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            e.message?.let { LogUtil.e(it) }
            Toast.makeText(requireContext(), "匯出失敗: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 觀察 ViewModel 狀態變化
     */
    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            settingViewModel.uiState.collectLatest { state ->
                when (state) {
                    is SettingUiState.ExportSuccess -> {
                        loadingDialog.dismiss()
                        Toast.makeText(requireContext(), "匯出成功", Toast.LENGTH_SHORT).show()
                    }

                    is SettingUiState.ExportError -> {
                        loadingDialog.dismiss()
                        Toast.makeText(
                            requireContext(),
                            "匯出失敗: ${state.message}",
                            Toast.LENGTH_SHORT
                        ).show()
                    }

                    is SettingUiState.ImportSuccess -> {
                        loadingDialog.dismiss()
                        Toast.makeText(
                            requireContext(),
                            "匯入完成：成功 ${state.successCount} 筆，失敗 ${state.errorCount} 筆",
                            Toast.LENGTH_LONG
                        ).show()
                        // 重新載入資料
                        initData()
                    }

                    is SettingUiState.ImportError -> {
                        loadingDialog.dismiss()
                        Toast.makeText(
                            requireContext(),
                            "匯入失敗: ${state.message}",
                            Toast.LENGTH_SHORT
                        ).show()
                    }

                    else -> {
                        // 其他狀態不處理
                    }
                }
            }
        }
    }

    /**
     * 初始化選擇模式 UI
     */
    private fun initSelectionModeUI() {
        binding.btnSelectAll.setOnClickListener {
            if (birthDataItemAdapter.isAllSelected()) {
                birthDataItemAdapter.unselectAll()
                Toast.makeText(requireContext(), "已取消全選", Toast.LENGTH_SHORT).show()
            } else {
                birthDataItemAdapter.selectAll()
                Toast.makeText(requireContext(), "已全選 ${birthDataItemAdapter.itemCount} 項", Toast.LENGTH_SHORT).show()
            }
        }

        binding.btnDeleteSelected.setOnClickListener {
            deleteSelectedItems()
        }
    }

    /**
     * 切換選擇模式
     */
    private fun toggleSelectionMode() {
        isSelectionMode = !isSelectionMode
        if (isSelectionMode) {
            enterSelectionMode()
        } else {
            exitSelectionMode()
        }
    }

    /**
     * 進入選擇模式
     */
    private fun enterSelectionMode() {
        isSelectionMode = true

        // 顯示選擇工具列動畫
        binding.selectionToolbar.visibility = View.VISIBLE
        binding.selectionToolbar.translationY = binding.selectionToolbar.height.toFloat()
        binding.selectionToolbar.animate()
            .translationY(0f)
            .setDuration(300)
            .start()

        binding.fabAdd.hide()
        updateSelectionUI(birthDataItemAdapter.getSelectedCount())

        Toast.makeText(requireContext(), "已進入選擇模式", Toast.LENGTH_SHORT).show()
    }

    /**
     * 退出選擇模式
     */
    private fun exitSelectionMode() {
        isSelectionMode = false

        // 隱藏選擇工具列動畫
        binding.selectionToolbar.animate()
            .translationY(binding.selectionToolbar.height.toFloat())
            .setDuration(300)
            .withEndAction {
                binding.selectionToolbar.visibility = View.GONE
            }
            .start()

        binding.fabAdd.show()
        birthDataItemAdapter.unselectAll()
    }

    /**
     * 更新選擇模式 UI
     */
    private fun updateSelectionUI(selectedCount: Int) {
        if (!isSelectionMode) return

        val totalCount = birthDataItemAdapter.itemCount
        binding.tvSelectedCount.text = if (selectedCount == 0) {
            "未選擇任何項目"
        } else {
            "已選擇 $selectedCount / $totalCount 項"
        }

        binding.btnSelectAll.text = if (birthDataItemAdapter.isAllSelected()) "取消全選" else "全選"
        binding.btnDeleteSelected.isEnabled = selectedCount > 0

        // 更新刪除按鈕的透明度
        binding.btnDeleteSelected.alpha = if (selectedCount > 0) 1.0f else 0.5f
    }

    /**
     * 刪除選中的項目
     */
    private fun deleteSelectedItems() {
        val selectedItems = birthDataItemAdapter.getSelected()
        if (selectedItems.isEmpty()) {
            Toast.makeText(requireContext(), "請先選擇要刪除的項目", Toast.LENGTH_SHORT).show()
            return
        }

        val dialog = MaterialDialog(requireContext(), MaterialDialog.DEFAULT_BEHAVIOR)
        dialog.message(null, "確定要刪除選中的 ${selectedItems.size} 個記錄嗎？此操作無法復原。", null)
        dialog.positiveButton(null, getString(R.string.yes)) {
            performBatchDelete(selectedItems)
        }
        dialog.negativeButton(null, getString(R.string.no)) {
            dialog.dismiss()
        }
        dialog.show()
    }

    /**
     * 執行批量刪除操作
     */
    private fun performBatchDelete(selectedItems: List<BirthData>) {
        val deleteDialog = LoadingDialog(requireContext())
        deleteDialog.show()

        lifecycleScope.launch {
            try {
                var successCount = 0
                var errorCount = 0

                selectedItems.forEach { birthData ->
                    try {
                        birthDataBox!!.remove(birthData)
                        successCount++
                    } catch (e: Exception) {
                        errorCount++
                        LogUtil.e("刪除記錄失敗: ${e.message}")
                    }
                }

                deleteDialog.dismiss()
                exitSelectionMode()
                initData()

                val message = if (errorCount == 0) {
                    "已成功刪除 $successCount 個記錄"
                } else {
                    "刪除完成：成功 $successCount 個，失敗 $errorCount 個"
                }
                Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()

            } catch (e: Exception) {
                deleteDialog.dismiss()
                LogUtil.e("批量刪除失敗: ${e.message}")
                Toast.makeText(requireContext(), "刪除失敗: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }
}