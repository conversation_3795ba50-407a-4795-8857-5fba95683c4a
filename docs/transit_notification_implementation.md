# 行運通知點擊功能實現文件

## 功能概述
實現了 `showTransitNotification` 點擊後直接進入行運盤 (Chart.Transit) 的功能。

## 實現流程

### 1. 通知創建 (NotificationUtil.kt)
- `showTransitNotification` 方法創建行運推播通知
- `createTransitNotificationIntent` 方法創建點擊意圖，指向 `SplashActivity`
- 意圖包含以下資料：
  - `action`: "ACTION_TRANSIT_NOTIFICATION"
  - `birth_data`: 出生資料 (BirthData)
  - `event_count`: 事件數量
  - `first_event_title`: 第一個事件標題

### 2. SplashActivity 處理
- `SplashActivity` 接收通知點擊意圖
- `toMainActivity()` 方法將所有 intent extras 傳遞給 `MainActivity`

### 3. MainActivity 處理 (新增功能)
- 在 `onCreate` 方法中調用 `handleTransitNotificationIntent()`
- `handleTransitNotificationIntent()` 方法：
  - 檢查 intent action 是否為 "ACTION_TRANSIT_NOTIFICATION"
  - 提取出生資料和事件資訊
  - 調用 `navigateToTransitChart()` 導航到行運盤

### 4. 導航到行運盤
- `navigateToTransitChart()` 方法：
  - 創建包含出生資料和 Chart.Transit 的 Bundle
  - 啟動 `SignDetailActivity` 顯示行運盤

## 程式碼修改

### MainActivity.kt 新增內容

#### 導入
```kotlin
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.util.astro.ChartUtils.toStorageValue
```

#### 新增方法
```kotlin
/**
 * 處理行運通知點擊意圖
 */
private fun handleTransitNotificationIntent() {
    val action = intent.action
    LogUtil.d("MainActivity: 接收到意圖 action = $action")
    
    if (action == "ACTION_TRANSIT_NOTIFICATION") {
        val birthData = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra("birth_data", BirthData::class.java)
        } else {
            @Suppress("DEPRECATION")
            intent.getParcelableExtra<BirthData>("birth_data")
        }
        
        val eventCount = intent.getIntExtra("event_count", 0)
        val firstEventTitle = intent.getStringExtra("first_event_title")
        
        LogUtil.d("MainActivity: 行運通知資料 - birthData: ${birthData?.name}, eventCount: $eventCount, firstEventTitle: $firstEventTitle")
        
        if (birthData != null) {
            // 導航到行運盤頁面
            navigateToTransitChart(birthData)
        } else {
            LogUtil.e("MainActivity: 行運通知中沒有找到出生資料")
            Toast.makeText(this, "無法載入行運資料", Toast.LENGTH_SHORT).show()
        }
    }
}

/**
 * 導航到行運盤頁面
 */
private fun navigateToTransitChart(birthData: BirthData) {
    try {
        LogUtil.d("MainActivity: 準備導航到行運盤頁面，出生資料: ${birthData.name}")
        
        val bundle = Bundle().apply {
            putParcelable(KeyDefine.UserBirthDataA, birthData)
            putString(KeyDefine.Chart, Chart.Transit.toStorageValue())
        }
        
        startActivity(SignDetailActivity::class.java, bundle)
        
        LogUtil.d("MainActivity: 成功啟動行運盤頁面")
    } catch (e: Exception) {
        LogUtil.e("MainActivity: 導航到行運盤頁面失敗: ${e.message}")
        Toast.makeText(this, "開啟行運盤失敗: ${e.message}", Toast.LENGTH_SHORT).show()
    }
}
```

## 使用方式

### 參考現有實現
```kotlin
val onClickListener = View.OnClickListener {
    val birthDataA = it.tag as BirthData
    val bundle = Bundle()
    bundle.putParcelable(KeyDefine.UserBirthDataA, birthDataA)
    bundle.putString(KeyDefine.Chart, Chart.Transit.toStorageValue())
    startActivity(SignDetailActivity::class.java, bundle)
}
SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
    requireActivity().supportFragmentManager,
    "SelectBottomSheetFragment"
)
```

## 測試方式

1. 發送行運推播通知
2. 點擊通知
3. 應用應該：
   - 啟動並進入 MainActivity
   - 自動導航到 SignDetailActivity
   - 顯示對應出生資料的行運盤

## 注意事項

1. 確保出生資料存在且有效
2. 處理 Android 版本差異 (API 33+ 的 Parcelable 處理)
3. 添加適當的錯誤處理和日誌記錄
4. 遵循現有的導航模式和程式碼風格

## 相關檔案

- `app/src/main/java/com/one/astrology/ui/activity/MainActivity.kt`
- `app/src/main/java/com/one/astrology/util/NotificationUtil.kt`
- `app/src/main/java/com/one/astrology/ui/activity/SplashActivity.kt`
- `app/src/main/java/com/one/astrology/ui/activity/SignDetailActivity.kt`
